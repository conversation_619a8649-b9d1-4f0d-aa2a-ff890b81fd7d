import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:workmanager/workmanager.dart';

import 'core/config/app_config.dart';
import 'core/constants/app_constants.dart';
import 'core/security/encryption_service.dart';
import 'core/security/biometric_service.dart';
import 'core/security/compliance_service.dart';
import 'features/offline_sync/data/offline_sync_manager.dart';
import 'features/mobile_money/data/services/mobile_money_service.dart';
import 'features/utilities/data/services/utility_service.dart';
import 'notifications/zambia_alert.dart';
import 'notifications/transaction_alert_integration.dart';
import 'offline/zambia_sync.dart';
import 'offline/zambia_retry_policy.dart';
import 'offline/zambia_data_saver.dart';
import 'offline/zambia_sms_gateway.dart';
import 'services/agent_service.dart';
import 'services/zambia_central_registry.dart';
import 'presentation/splash/splash_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services
  await _initializeServices();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const ZambiaPayApp());
}

/// Initialize all core services
Future<void> _initializeServices() async {
  try {
    // Initialize encryption service first
    await EncryptionService().initialize();

    // Initialize compliance and audit service
    await ComplianceService().initialize();

    // Initialize offline sync manager
    await OfflineSyncManager().initialize();

    // Initialize mobile money service with demo credentials
    await MobileMoneyService().initialize(
      mtnCredentials: {
        'apiKey': 'demo_mtn_api_key',
        'subscriptionKey': 'demo_mtn_subscription_key',
      },
      airtelCredentials: {
        'clientId': 'demo_airtel_client_id',
        'clientSecret': 'demo_airtel_client_secret',
      },
      zamtelCredentials: {
        'apiKey': 'demo_zamtel_api_key',
        'merchantId': 'demo_zamtel_merchant_id',
      },
    );

    // Initialize utility service
    await UtilityService().initialize(
      zescoCredentials: {
        'apiKey': 'demo_zesco_api_key',
        'merchantId': 'demo_zesco_merchant_id',
      },
    );

    // Initialize Zambian alert system
    await _initializeZambianAlertSystem();

    // Initialize Zambian offline sync system
    await _initializeZambianOfflineSync();

    // Initialize Zambian agent service
    await _initializeZambianAgentService();

    print('✅ All services initialized successfully');
  } catch (e) {
    print('❌ Service initialization failed: $e');
  }
}

class ZambiaPayApp extends StatelessWidget {
  const ZambiaPayApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConfig.appName,
      debugShowCheckedModeBanner: false,

      // Localization
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppConfig.supportedLanguages.map((lang) => Locale(lang)).toList(),

      // Theme
      theme: _buildTheme(),

      // Home
      home: const SplashScreen(),
    );
  }

  ThemeData _buildTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF2E7D32), // Zambian green
        brightness: Brightness.light,
      ),
      textTheme: GoogleFonts.robotoTextTheme(),
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.black87,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(double.infinity, AppConstants.buttonHeight),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding,
          vertical: AppConstants.defaultPadding,
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
      ),
    );
  }
}

/// Initialize Zambian alert system
Future<void> _initializeZambianAlertSystem() async {
  try {
    print('🔔 Initializing Zambian alert system...');

    // Initialize the alert service
    final alertService = ZambiaAlertService();
    await alertService.initialize();

    // Setup tiered transaction alerts
    alertService.setupTransactionAlerts();

    // Initialize transaction alert integration
    final alertIntegration = TransactionAlertIntegration();
    await alertIntegration.initialize();

    print('✅ Zambian alert system initialized successfully');
  } catch (e) {
    print('❌ Zambian alert system initialization failed: $e');
  }
}

/// Initialize Zambian offline sync system
Future<void> _initializeZambianOfflineSync() async {
  try {
    print('🔄 Initializing Zambian offline sync system...');

    // Initialize the sync engine
    final syncEngine = ZambiaSyncEngine();
    await syncEngine.initialize();

    // Initialize SMS gateway for emergency fallback
    final smsGateway = ZambiaSMSGateway();
    await smsGateway.initialize();

    // Create retry policy optimized for Zambian conditions
    final retryPolicy = ZambiaRetryPolicy(
      connectivityLevel: ConnectivityLevel.stable2G,
      season: ZambianSeason.drySeasonStable,
      priority: SyncPriority.medium,
    );

    // Create data saver for 2G optimization
    final dataSaver = ZambiaDataSaver(
      connectivityLevel: ConnectivityLevel.stable2G,
    );

    // Configure sync engine for Zambian connectivity
    syncEngine.optimizeForZMConnectivity();

    // Apply configuration
    SyncEngine.configure(
      retryPolicy: retryPolicy,
      dataConservation: dataSaver,
      emergencyFallback: smsGateway,
    );

    print('✅ Zambian offline sync system initialized successfully');
  } catch (e) {
    print('❌ Zambian offline sync system initialization failed: $e');
  }
}

/// Initialize Zambian agent service
Future<void> _initializeZambianAgentService() async {
  try {
    print('🏪 Initializing Zambian agent service...');

    // Initialize Zambia Central Registry
    await ZambiaCentralRegistry.initialize();

    // Initialize Agent Service
    final agentService = AgentService();
    await agentService.initialize();

    // Load production agents with filtering
    agentService.loadProductionAgents();

    print('✅ Zambian agent service initialized successfully');
  } catch (e) {
    print('❌ Zambian agent service initialization failed: $e');
  }
}


