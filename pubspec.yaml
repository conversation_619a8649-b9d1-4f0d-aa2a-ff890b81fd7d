name: pay_mule
description: Pay Mule - A comprehensive Flutter app for Zambia solving financial exclusion and utility fragmentation with offline-first architecture.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # Core Dependencies
  cupertino_icons: ^1.0.6

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # Database & Storage
  sqflite: ^2.3.0
  sqflite_sqlcipher: ^2.2.1  # AES-256 encryption for SQLite
  path: ^1.8.3
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0

  # Network & Connectivity
  http: ^1.1.0
  dio: ^5.3.2
  connectivity_plus: ^5.0.1
  internet_connection_checker: ^1.0.0+1

  # Encryption & Security
  crypto: ^3.0.3
  encrypt: ^5.0.1
  pointycastle: ^3.7.3

  # Mobile Money & Payment APIs
  uuid: ^4.1.0
  intl: 0.20.2

  # Background Processing
  workmanager: 0.8.0

  # UI & UX
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.7
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0

  # Utilities
  logger: ^2.0.2+1
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0
  permission_handler: ^11.0.1

  # QR Code & Scanning
  qr_flutter: ^4.1.0
  mobile_scanner: ^3.5.2

  # Biometric Authentication
  local_auth: ^2.1.6

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  mockito: ^5.4.2
  bloc_test: ^9.1.4
  integration_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/
    - assets/translations/


